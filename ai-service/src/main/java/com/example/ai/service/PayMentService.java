package com.example.ai.service;

import com.example.common.entity.dto.OrderDto;
import com.example.common.entity.vo.OrderVo;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * Create by 2025/7/23 17:13
 * desc 支付vip
 */
public interface PayMentService {

    List<VipPlanVo> getVipPlans();

    List<PaymentMethodVo> paymentMethodList();

    Result getRechargeList();

    Result getAliPayCode();

    Result getOrderNum();

    ConcurrentHashMap<String, Object> getPagePayCode(String payType);

    String createOrder(OrderDto orderDto);

    List<OrderVo> orderLists();
}
