package com.example.ai.feign;

import com.example.common.entity.dto.OrderDto;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import com.example.common.feign.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Payment服务Feign客户端
 * 用于调用payment-service的相关接口
 * 使用统一的Feign配置和降级策略
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@FeignClient(
    name = "payment-service",
    configuration = FeignConfig.class,
    fallbackFactory = PaymentFeignFallbackFactory.class
)
public interface PayMentFeign {


    /**
     * 获取所有VIP套餐功能特性
     *
     * @return VIP套餐列表
     */
    @GetMapping("/vip-plan/getAllVipPlansFeatures")
    Result<List<VipPlanVo>> getAllVipPlansFeatures();

    /**
     * 获取支付方式列表
     *
     * @return 支付方式列表
     */
    @GetMapping("/payment-method/paymentMethodList")
    Result<List<PaymentMethodVo>> paymentMethodList();

    /**
     * 获取充值列表
     *
     * @return 充值列表
     */
    @GetMapping("/recharge/getList")
    Result getRechargeList();

    /**
     * 获取支付宝支付码（测试接口）
     *
     * @return 支付码信息
     */
    @GetMapping("/test/test1")
    Result getAliPayCode();

    /**
     * 获取订单号
     *
     * @return 订单号
     */
    @GetMapping("/order/getOrderNum")
    Result getOrderNum();

    /**
     * 根据支付类型获取预支付下单页面以及唯一订单号
     *
     * @param payType 支付类型
     * @return 支付页面信息
     */
    @PostMapping("/order/getPagePayCode")
    Result getPagePayCode(@RequestParam("payType") String payType);

    /***
     * 创建订单并且返回预支付订单页面
     * @param orderDto
     * @return
     */
    @PostMapping("/order/createOrder")
    Result createOrder(@RequestBody @Validated OrderDto orderDto);

    @GetMapping("/order/orderLists")
    Result orderLists();
}

