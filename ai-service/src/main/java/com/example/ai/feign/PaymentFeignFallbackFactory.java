package com.example.ai.feign;

import com.example.common.entity.dto.OrderDto;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import com.example.common.feign.fallback.BaseFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Payment服务降级工厂
 * 继承BaseFallbackFactory，提供统一的降级处理逻辑
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class PaymentFeignFallbackFactory extends BaseFallbackFactory<PayMentFeign> {

    public PaymentFeignFallbackFactory() {
        super("payment-service");
    }

    @Override
    public PayMentFeign create(Throwable cause) {
        return new PayMentFeign() {

            @Override
            public Result<List<VipPlanVo>> getAllVipPlansFeatures() {
                logFallback("getAllVipPlansFeatures", cause);

                // VIP套餐信息对用户体验很重要，返回错误信息提示用户
                return createErrorResult(cause);
            }

            @Override
            public Result<List<PaymentMethodVo>> paymentMethodList() {
                logFallback("paymentMethodList", cause);

                // 支付方式列表对支付流程很重要，返回错误信息
                return createErrorResult(cause);
            }

            @Override
            public Result getRechargeList() {
                logFallback("getRechargeList", cause);

                // 充值列表降级
                return createErrorResult(cause);
            }

            @Override
            public Result getAliPayCode() {
                logFallback("getAliPayCode", cause);

                // 测试接口降级
                return createErrorResult(cause);
            }

            @Override
            public Result getOrderNum() {
                logFallback("getOrderNum", cause);

                // 订单号生成失败，返回明确错误信息
                String errorMessage = extractErrorMessage(cause);
                return Result.error(503, "订单号生成失败: " + errorMessage);
            }

            @Override
            public Result getPagePayCode(String payType) {
                logFallback("getPagePayCode", cause);

                // 支付页面生成失败，这是关键功能，需要明确提示
                String errorMessage = extractErrorMessage(cause);
                return Result.error(503, "支付页面生成失败: " + errorMessage);
            }

            @Override
            public Result createOrder(OrderDto orderDto) {
                return null;
            }

            @Override
            public Result orderLists() {
                return null;
            }
        };
    }
}
