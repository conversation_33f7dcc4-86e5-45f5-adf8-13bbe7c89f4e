package com.example.common.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class OrderDto {

    /**
     * 订单ID（更新时需要）
     */
    private String id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private Long orderNum;

    /**
     * 订单类型：vip-购买VIP，recharge-充值
     */
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    /**
     * 订单金额（分为单位）
     */
    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    private BigDecimal amount;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 订单状态：pending-待支付，paid-已支付，cancelled-已取消，failed-支付失败
     */
    private String status;

    /**
     * 订单过期时间
     */
    private LocalDateTime expireTime;
}
