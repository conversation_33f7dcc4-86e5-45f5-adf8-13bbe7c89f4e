package com.example.payment.util;

import com.example.common.entity.dto.OrderDto;
import com.example.common.entity.vo.OrderVo;
import com.example.payment.entity.*;
import com.example.payment.entity.dto.TransactionDto;
import com.example.payment.entity.dto.UserVipDto;
import com.example.payment.entity.dto.VipPlanDto;
import com.example.payment.entity.vo.TransactionVo;
import com.example.payment.entity.vo.UserVipVo;
import com.example.payment.entity.vo.VipPlanVo;
import com.example.payment.enums.OrderStatusEnum;
import com.example.payment.enums.OrderTypeEnum;
import com.example.payment.enums.VipLevelEnum;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 支付模块转换工具类
 * 用于Entity、DTO、VO之间的转换
 */
public class PaymentConverter {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // ==================== UserVip 转换 ====================

    /**
     * UserVipDto 转 UserVip
     */
    public static UserVip toEntity(UserVipDto dto) {
        if (dto == null) return null;
        UserVip entity = new UserVip();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * UserVip 转 UserVipVo
     */
    public static UserVipVo toVo(UserVip entity) {
        if (entity == null) return null;
        UserVipVo vo = new UserVipVo();
        BeanUtils.copyProperties(entity, vo);

        // 设置VIP等级名称
        VipLevelEnum levelEnum = VipLevelEnum.getByCode(entity.getVipLevel());
        vo.setVipLevelName(levelEnum != null ? levelEnum.getName() : entity.getVipLevel());

        // 格式化金额显示
        vo.setBalanceFormatted(formatAmount(entity.getBalance()));
        vo.setTotalSpentFormatted(formatAmount(entity.getTotalSpent()));

        // 计算是否过期和剩余天数
        if (entity.getExpireTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            vo.setIsExpired(entity.getExpireTime().isBefore(now));
            vo.setRemainingDays(ChronoUnit.DAYS.between(now, entity.getExpireTime()));
        }

        return vo;
    }

    // ==================== VipPlan 转换 ====================

    /**
     * VipPlanDto 转 VipPlan
     */
    public static VipPlan toEntity(VipPlanDto dto) {
        if (dto == null) return null;
        VipPlan entity = new VipPlan();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * VipPlan 转 VipPlanVo
     */
    public static VipPlanVo toVo(VipPlan entity) {
        if (entity == null) return null;
        VipPlanVo vo = new VipPlanVo();
        BeanUtils.copyProperties(entity, vo);

        // 格式化价格显示
        vo.setMonthlyPriceFormatted(formatAmount(entity.getMonthlyPrice()));
        vo.setYearlyPriceFormatted(formatAmount(entity.getYearlyPrice()));

        // 计算年付优惠
        if (entity.getMonthlyPrice() != null && entity.getYearlyPrice() != null) {
            long yearlyByMonth = entity.getMonthlyPrice() * 12;
            long discount = yearlyByMonth - entity.getYearlyPrice();
            vo.setYearlyDiscountFormatted(formatAmount(discount));
        }

        // 设置限制显示文本
        vo.setDailyMessageLimitText(entity.getDailyMessageLimit() == -1 ? "无限制" : entity.getDailyMessageLimit() + "条/天");
        vo.setHistoryDaysText(entity.getHistoryDays() == -1 ? "永久保存" : entity.getHistoryDays() + "天");
        vo.setStatusText(entity.getStatus() == 1 ? "正常" : "下架");

        return vo;
    }

    // ==================== Order 转换 ====================

    /**
     * OrderDto 转 Order
     */
    public static Order toEntity(OrderDto dto) {
        if (dto == null) return null;
        Order entity = new Order();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * Order 转 OrderVo
     */
    public static OrderVo toVo(Order entity) {
        if (entity == null) return null;
        OrderVo vo = new OrderVo();
        BeanUtils.copyProperties(entity, vo);

        // 设置订单类型文本
        OrderTypeEnum typeEnum = OrderTypeEnum.getByCode(entity.getOrderType());
        vo.setOrderTypeText(typeEnum != null ? typeEnum.getName() : entity.getOrderType());

        // 设置订单状态文本和颜色
        OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(entity.getStatus());
        vo.setStatusText(statusEnum != null ? statusEnum.getName() : entity.getStatus());
        vo.setStatusColor(getStatusColor(entity.getStatus()));

//        // 格式化金额
//        vo.setAmountFormatted(formatAmount(entity.getAmount()));

        // 设置操作权限
        vo.setCanPay("pending".equals(entity.getStatus()));
        vo.setCanCancel("pending".equals(entity.getStatus()));

        return vo;
    }

    // ==================== Transaction 转换 ====================

    /**
     * TransactionDto 转 Transaction
     */
    public static Transaction toEntity(TransactionDto dto) {
        if (dto == null) return null;
        Transaction entity = new Transaction();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * Transaction 转 TransactionVo
     */
    public static TransactionVo toVo(Transaction entity) {
        if (entity == null) return null;
        TransactionVo vo = new TransactionVo();
        BeanUtils.copyProperties(entity, vo);

        // 设置交易类型文本
        vo.setTypeText(getTransactionTypeText(entity.getType()));

        // 格式化金额和余额
        vo.setAmountFormatted(formatAmount(entity.getAmount()));
        vo.setBalanceFormatted(formatAmount(entity.getBalance()));

        // 设置金额符号
        vo.setAmountSign(isIncomeTransaction(entity.getType()) ? "+" : "-");

        // 设置状态文本和颜色
        vo.setStatusText(getTransactionStatusText(entity.getStatus()));
        vo.setStatusColor(getTransactionStatusColor(entity.getStatus()));

        return vo;
    }

    // ==================== 工具方法 ====================

    /**
     * 格式化金额（分转元）
     */
    public static String formatAmount(Long amountInCents) {
        if (amountInCents == null) return "0.00";
        BigDecimal amount = new BigDecimal(amountInCents).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return amount.toString();
    }

    /**
     * 获取订单状态颜色
     */
    private static String getStatusColor(String status) {
        switch (status) {
            case "pending": return "#f39c12";
            case "paid": return "#27ae60";
            case "cancelled": return "#95a5a6";
            case "failed": return "#e74c3c";
            default: return "#95a5a6";
        }
    }

    /**
     * 获取交易类型文本
     */
    private static String getTransactionTypeText(String type) {
        switch (type) {
            case "recharge": return "充值";
            case "consume": return "消费";
            case "vip": return "购买VIP";
            case "refund": return "退款";
            default: return type;
        }
    }

    /**
     * 获取交易状态文本
     */
    private static String getTransactionStatusText(String status) {
        switch (status) {
            case "success": return "成功";
            case "pending": return "处理中";
            case "failed": return "失败";
            default: return status;
        }
    }

    /**
     * 获取交易状态颜色
     */
    private static String getTransactionStatusColor(String status) {
        switch (status) {
            case "success": return "#27ae60";
            case "pending": return "#f39c12";
            case "failed": return "#e74c3c";
            default: return "#95a5a6";
        }
    }

    /**
     * 判断是否为收入类型交易
     */
    private static boolean isIncomeTransaction(String type) {
        return "recharge".equals(type) || "refund".equals(type);
    }
}
