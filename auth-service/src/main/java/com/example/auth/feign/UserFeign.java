package com.example.auth.feign;

import com.example.common.result.Result;
import com.example.common.dto.UserDto;
import com.example.common.feign.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 用户服务Feign客户端
 * 用于调用user-service的相关接口
 */
@FeignClient(
    name = "user-service",
    configuration = FeignConfig.class,
    fallbackFactory = UserFeignFallbackFactory.class
)
public interface UserFeign {

    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/user/auth/username/{username}")
    Result<UserDto> getUserByUsername(@PathVariable("username") String username);

    /**
     * 验证用户密码
     */
    @PostMapping("/user/auth/validate")
    Result<Boolean> validatePassword(@RequestParam("username") String username,
                                   @RequestParam("password") String password);

    /**
     * 创建新用户
     */
    @PostMapping("/user/auth/create")
    Result<UserDto> createUser(@RequestBody UserDto user);

    /**
     * 更新最后登录时间
     */
    @PostMapping("/user/auth/login-time/{userId}")
    Result<Void> updateLastLoginTime(@PathVariable("userId") Long userId);

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/user/auth/exists/username/{username}")
    Result<Boolean> existsByUsername(@PathVariable("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/user/auth/exists/email/{email}")
    Result<Boolean> existsByEmail(@PathVariable("email") String email);
}
