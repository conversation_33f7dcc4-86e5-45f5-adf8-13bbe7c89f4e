package com.example.auth.feign;

import com.example.common.result.Result;
import com.example.common.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务Feign降级工厂
 */
@Slf4j
@Component
public class UserFeignFallbackFactory implements FallbackFactory<UserFeign> {

    @Override
    public UserFeign create(Throwable cause) {
        return new UserFeign() {

            @Override
            public Result<UserDto> getUserByUsername(String username) {
                log.error("调用用户服务获取用户信息失败, username: {}", username, cause);
                return Result.error(503, "用户服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<Boolean> validatePassword(String username, String password) {
                log.error("调用用户服务验证密码失败, username: {}", username, cause);
                // 为了安全考虑，密码验证失败时返回false
                return Result.error(503, "用户服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<UserDto> createUser(UserDto user) {
                log.error("调用用户服务创建用户失败, username: {}", user.getUsername(), cause);
                return Result.error(503, "用户服务暂时不可用，请稍后重试");
            }

            @Override
            public Result updateLastLoginTime(Long userId) {
                log.error("调用用户服务更新登录时间失败, userId: {}", userId, cause);
                // 更新登录时间失败不影响主流程，返回成功
                return Result.success("登录时间更新失败，但不影响登录");
            }

            @Override
            public Result<Boolean> existsByUsername(String username) {
                log.error("调用用户服务检查用户名失败, username: {}", username, cause);
                return Result.error(503, "用户服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<Boolean> existsByEmail(String email) {
                log.error("调用用户服务检查邮箱失败, email: {}", email, cause);
                return Result.error(503, "用户服务暂时不可用，请稍后重试");
            }
        };
    }
}
