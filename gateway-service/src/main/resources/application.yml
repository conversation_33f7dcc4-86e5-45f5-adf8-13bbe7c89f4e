
# ==========================================
# Spring Cloud Gateway 网关服务配置文件
# 纯网关服务，不依赖数据库和缓存
# ==========================================

# 服务器配置
server:
  port: 8081  # 网关服务端口，作为所有微服务的统一入口

# Spring框架配置
spring:
  application:
    name: gateway-service  # 服务名称，用于服务注册和发现

  # Spring Cloud Gateway配置
  cloud:
    # Nacos服务注册与发现配置
    nacos:
      discovery:
        server-addr: ************:8848  # Nacos服务器地址（线上环境）
        namespace: # 命名空间，用于环境隔离
        group: DEFAULT_GROUP  # 服务分组
        cluster-name: DEFAULT  # 集群名称
        metadata:  # 服务元数据
          version: 1.0.0
          description: "API网关服务，统一入口和路由转发"
          contact: "<EMAIL>"

    # Spring Cloud Gateway路由配置
    gateway:
      # 路由规则配置
      routes:
        # 认证服务路由
        - id: auth-service
          uri: lb://auth-service  # 负载均衡到auth-service
          predicates:
            - Path=/auth/**  # 匹配/auth/**路径
          filters:
            - StripPrefix=0  # 不去除路径前缀

        # 用户服务路由
        - id: user-service
          uri: lb://user-service  # 负载均衡到user-service
          predicates:
            - Path=/user/**  # 匹配/user/**路径
          filters:
            - StripPrefix=0  # 不去除路径前缀

        # AI服务路由
        - id: ai-service
          uri: lb://ai-service  # 负载均衡到ai-service
          predicates:
            - Path=/ai/**  # 匹配/ai/**路径
          filters:
            - StripPrefix=0  # 不去除路径前缀

        # AI业务服务路由
        - id: ai-business-service
          uri: lb://ai-business-service  # 负载均衡到ai-business-service
          predicates:
            - Path=/business/**  # 匹配/business/**路径
          filters:
            - StripPrefix=0  # 不去除路径前缀

        # 支付服务路由
        - id: payment-service
          uri: lb://payment-service  # 负载均衡到payment-service
          predicates:
            - Path=/payment/**  # 匹配/payment/**路径
          filters:
            - StripPrefix=0  # 不去除路径前缀

      # 全局过滤器配置
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
        - AddResponseHeader=Access-Control-Allow-Origin, *
        - AddResponseHeader=Access-Control-Allow-Methods, GET,POST,PUT,DELETE,OPTIONS
        - AddResponseHeader=Access-Control-Allow-Headers, Content-Type,Authorization,X-Requested-With

      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"  # 允许所有来源
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"  # 允许所有请求头
            allowCredentials: true  # 允许携带凭证
            maxAge: 3600  # 预检请求缓存时间

# JWT配置
jwt:
  secret: mySecretKeyForJWTTokenGenerationAndValidation  # JWT签名密钥
  expiration: 86400000  # Token过期时间(毫秒) - 24小时
  header: Authorization  # Token在请求头中的字段名
  prefix: "Bearer "  # Token前缀

# 日志配置 - 简化版本，减少冗余信息
logging:
  level:
    # 根日志级别设置为INFO
    root: info
    # 网关服务核心包使用INFO级别
    com.example.gateway: info  # 自定义包的日志级别
    # 框架日志设置为WARN级别
    org.springframework.cloud.gateway: warn  # Gateway日志级别
    # Nacos日志设置为WARN级别
    com.alibaba.nacos: warn
  pattern:
    # 简化的日志输出格式
    console: "%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n"
  file:
    name: logs/gateway-service.log  # 日志文件路径
    max-size: 50MB  # 减小单个日志文件大小
    max-history: 15  # 减少保留的日志文件数量

# 管理端点配置 - 用于健康检查和监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway  # 暴露的端点，包括gateway端点
  endpoint:
    health:
      show-details: always  # 显示健康检查详情
    gateway:
      enabled: true  # 启用Gateway端点，可以查看路由信息
  metrics:
    export:
      prometheus:
        enabled: true  # 启用Prometheus指标导出
