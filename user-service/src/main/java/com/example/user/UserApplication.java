package com.example.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 用户服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.example.user", "com.example.common"})
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.example.user.mapper")
public class UserApplication {

    public static void main(String[] args) {
        //解决bootstrap.yml 文件找不到问题 boot版本高于2.4
        System.setProperty("spring.cloud.bootstrap.enabled", "true");
        SpringApplication.run(UserApplication.class, args);
    }
}
