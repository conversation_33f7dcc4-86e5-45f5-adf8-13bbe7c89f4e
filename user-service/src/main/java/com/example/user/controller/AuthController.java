package com.example.user.controller;

import com.example.common.result.Result;
import com.example.common.dto.UserDto;
import com.example.user.entity.User;
import com.example.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 用户认证相关控制器
 * 提供给认证服务调用的接口
 */
@Slf4j
@RestController
@RequestMapping("/user/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    /**
     * 根据用户名获取用户信息（用于登录验证）
     */
    @GetMapping("/username/{username}")
    public Result<UserDto> getUserByUsername(@PathVariable String username) {
        log.info("Getting user by username for auth: {}", username);
        User user = userService.getUserByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 转换为DTO
        UserDto userDto = new UserDto();
        BeanUtils.copyProperties(user, userDto);
        return Result.success(userDto);
    }

    /**
     * 验证用户密码
     */
    @PostMapping("/validate")
    public Result<Boolean> validatePassword(@RequestParam String username,
                                          @RequestParam String password) {
        log.info("Validating password for user: {}", username);
        boolean isValid = userService.validatePassword(username, password);
        return Result.success(isValid);
    }

    /**
     * 创建新用户（用于注册）
     */
    @PostMapping("/create")
    public Result<UserDto> createUser(@RequestBody UserDto userDto) {
        log.info("Creating user: {}", userDto.getUsername());

        // 检查用户名是否已存在
        if (userService.existsByUsername(userDto.getUsername())) {
            return Result.error("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userDto.getEmail() != null && userService.existsByEmail(userDto.getEmail())) {
            return Result.error("邮箱已被使用");
        }

        // 转换为实体
        User user = new User();
        BeanUtils.copyProperties(userDto, user);

        User createdUser = userService.createUser(user);

        // 转换为DTO并清除密码字段
        UserDto resultDto = new UserDto();
        BeanUtils.copyProperties(createdUser, resultDto);
        resultDto.setPassword(null);

        return Result.success(resultDto);
    }

    /**
     * 更新最后登录时间
     */
    @PostMapping("/login-time/{userId}")
    public Result updateLastLoginTime(@PathVariable Long userId) {
        log.info("Updating last login time for user: {}", userId);
        userService.updateLastLoginTime(userId);
        return Result.success("更新成功");
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/exists/username/{username}")
    public Result<Boolean> existsByUsername(@PathVariable String username) {
        boolean exists = userService.existsByUsername(username);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/exists/email/{email}")
    public Result<Boolean> existsByEmail(@PathVariable String email) {
        boolean exists = userService.existsByEmail(email);
        return Result.success(exists);
    }
}
